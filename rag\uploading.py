import asyncio
from rag.embedding import async_batch_upload_knowledge
from rag.chunking_new import load_and_chunk_base_optimized


async def optimized_upload_strategy(agent: dict, knowledge_list: list):
    """异步并行上传策略（支持失败重试）"""
    
    # 1. 全量分块（保持当前逻辑）
    chunk_list = load_and_chunk_base_optimized(knowledge_list)
    
    # 2. 并行上传参数配置
    DATABASE = 'CSKB_' + agent['agent_name_en']
    MAX_CONCURRENT_UPLOADS = 8  # 并行上传任务数
    BATCH_SIZE = 100  # 单次请求批次大小
    
    # 3. 按类型分组，减少索引切换
    from collections import defaultdict
    type_batches = defaultdict(list)
    
    for chunk in chunk_list:
        collection = chunk.metadata['type'].split('_')[1]
        type_batches[collection].append({
            "text": chunk.page_content,
            "metadata": chunk.metadata
        })
    

    async def _upload_batch(collection: str, items: list):
        """带重试机制的单个批次上传"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                result = await async_batch_upload_knowledge(
                    items=items,
                    collection=collection,
                    database=DATABASE,
                    batch_size=BATCH_SIZE
                )
                print(f"✓ 成功上传 {collection}: {len(items)} 条")
                return result
            except Exception as e:
                if attempt == max_retries - 1:
                    print(f"× 上传失败 {collection}: {str(e)}")
                    raise
                await asyncio.sleep(2 ** attempt)
    
    # 创建所有上传任务
    tasks = []
    for collection, items in type_batches.items():
        # 拆分超大请求为批次
        for i in range(0, len(items), BATCH_SIZE):
            batch = items[i:i+BATCH_SIZE]
            task = _upload_batch(collection, batch)
            tasks.append(task)
    
    # 使用信号量控制并发数
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_UPLOADS)
    async with semaphore:
        results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 统计上传结果
    success_count = sum(1 for r in results if not isinstance(r, Exception))
    print(f"上传完成！成功批次: {success_count}/{len(tasks)}")