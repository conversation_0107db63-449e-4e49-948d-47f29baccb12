import pymysql
from dbutils.pooled_db import PooledDB
import logging
from typing import List, Tuple, Dict, Any

import os
import sys
# 添加上上层目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from config import dbinfo

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MySQLDatabase:
    """
    MySQL数据库操作类，实现连接池管理、参数化查询和资源释放
    """
    def __init__(self, config_key: str, charset: str = 'utf8mb4',
                 maxconnections: int = 5, mincached: int = 1, maxcached: int = 3):
        """
        初始化数据库连接池
        :param config_key: 配置文件中的数据库标识key
        :param charset: 字符集
        :param maxconnections: 连接池最大连接数
        :param mincached: 连接池最小空闲连接数
        :param maxcached: 连接池最大空闲连接数
        """
        # 从配置文件获取数据库连接信息
        try:
            host, port, user, password, database = dbinfo(config_key)
        except Exception as e:
            logger.error(f"获取数据库配置失败: {str(e)}")
            raise ValueError(f"无效的数据库配置key: {config_key}") from e

        self.pool = PooledDB(
            creator=pymysql,
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset=charset,
            maxconnections=maxconnections,
            mincached=mincached,
            maxcached=maxcached,
            cursorclass=pymysql.cursors.DictCursor
        )

    def get_connection(self):
        """
        获取数据库连接
        :return: 数据库连接对象
        """
        try:
            return self.pool.connection()
        except Exception as e:
            logger.error(f"获取数据库连接失败: {str(e)}")
            raise

    def execute_query(self, sql: str, params: Tuple[Any] = None) -> List[Dict[str, Any]]:
        """
        执行查询SQL
        :param sql: 查询SQL语句
        :param params: SQL参数
        :return: 查询结果列表
        """
        connection = None
        cursor = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            cursor.execute(sql, params or ())
            result = cursor.fetchall()
            logger.info(f"执行查询成功，影响行数: {cursor.rowcount}")
            return result
        except Exception as e:
            logger.error(f"执行查询失败: {str(e)}, SQL: {sql}, 参数: {params}")
            raise
        finally:
            self._close_resources(connection, cursor)

    def execute_update(self, sql: str, params: Tuple[Any] = None) -> int:
        """
        执行更新SQL(INSERT/UPDATE/DELETE)
        :param sql: 更新SQL语句
        :param params: SQL参数
        :return: 影响行数
        """
        connection = None
        cursor = None
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            affected_rows = cursor.execute(sql, params or ())
            connection.commit()
            logger.info(f"执行更新成功，影响行数: {affected_rows}")
            return affected_rows
        except Exception as e:
            if connection:
                connection.rollback()
            logger.error(f"执行更新失败: {str(e)}, SQL: {sql}, 参数: {params}")
            raise
        finally:
            self._close_resources(connection, cursor)

    def _close_resources(self, connection, cursor):
        """
        关闭数据库资源
        :param connection: 数据库连接
        :param cursor: 游标对象
        """
        if cursor:
            try:
                cursor.close()
            except Exception as e:
                logger.warning(f"关闭游标失败: {str(e)}")
        if connection:
            try:
                connection.close()  # 将连接返回给连接池
            except Exception as e:
                logger.warning(f"关闭连接失败: {str(e)}")

    def close(self):
        """
        显式关闭连接池
        """
        if hasattr(self, 'pool'):
            self.pool.close()
            logger.info("数据库连接池已关闭")

    def __del__(self):
        """
        析构函数中尝试关闭连接池
        """
        self.close()


if __name__ == "__main__":
    db = MySQLDatabase("cskb")
    result = db.execute_query("SELECT id,title FROM cskb_doc_pub limit 10;")
    print(result)
    db.close()