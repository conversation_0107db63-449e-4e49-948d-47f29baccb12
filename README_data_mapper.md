# MySQL字段映射器使用指南

## 概述

`utils/data_mapper.py` 提供了一个完整的MySQL查询结果字段映射解决方案，用于将MySQL表字段名转换为标准的目标字段名。

## 主要功能

### 1. 字段映射配置

**文档表字段映射 (DOC_FIELD_MAPPING):**
```python
{
    'id': 'id',
    'original_id': 'originalId',
    'agent_id': 'agentId',
    'plain_text': 'docText',        # MySQL字段 -> 目标字段
    'rich_text': 'docHtml',
    'pub_user_name': 'pubUserName',
    'word_num': 'wordNum',
    # ... 更多映射
}
```

**FAQ表字段映射 (FAQ_FIELD_MAPPING):**
```python
{
    'id': 'id',
    'agent_id': 'agentId',
    'question': 'question',
    'answer': 'answer',
    'pub_user_name': 'pubUserName',
    'version_status': 'versionStatus',
    # ... 更多映射
}
```

### 2. 目标字段列表

**文档目标字段 (DOC_TARGET_FIELDS):**
```python
['id', 'originalId', 'id_parent', 'title', 'agentId', 'agentName', 
 'dirId', 'originalId_parent', 'pubUserName', 'pubTime', 'version', 
 'tags', 'attaches', 'wordNum', 'pictureNum', 'linkNum', 'docText', 
 'docHtml', 'dir_name', 'dir_level', 'versionStatus']
```

**FAQ目标字段 (FAQ_TARGET_FIELDS):**
```python
['id', 'agentId', 'agentName', 'question', 'answer', 'dirId', 
 'originalId', 'pubUserName', 'pubTime', 'versionStatus', 
 'dir_name', 'dir_level']
```

## 使用方法

### 1. 基本使用

```python
from utils.data_mapper import map_doc_result, map_faq_result

# 映射文档查询结果
mysql_doc_result = {
    'id': 'doc_001',
    'agent_id': 'agent_001',
    'title': '测试文档',
    'plain_text': '文档内容...',
    'pub_user_name': '张三'
}

mapped_doc = map_doc_result(mysql_doc_result, use_dict_cursor=True)
# 结果: {'id': 'doc_001', 'agentId': 'agent_001', 'title': '测试文档', 
#       'docText': '文档内容...', 'pubUserName': '张三', ...}

# 映射FAQ查询结果
mysql_faq_result = {
    'id': 'faq_001',
    'agent_id': 'agent_001',
    'question': '如何使用？',
    'answer': '请参考文档...',
    'version_status': 1
}

mapped_faq = map_faq_result(mysql_faq_result, use_dict_cursor=True)
# 结果: {'id': 'faq_001', 'agentId': 'agent_001', 'question': '如何使用？',
#       'answer': '请参考文档...', 'versionStatus': 1, ...}
```

### 2. 生成SQL字段列表

```python
from utils.data_mapper import get_doc_sql_fields, get_faq_sql_fields

# 获取文档表的MySQL字段列表
doc_fields = get_doc_sql_fields()
# 返回: "id, original_id, title, agent_id, plain_text, rich_text, ..."

# 生成SQL查询
sql = f"SELECT {doc_fields} FROM cskb_doc_pub WHERE agent_id = %s"
```

### 3. 在数据访问层中使用

```python
from database.mysql import MySQLDatabase
from utils.data_mapper import map_doc_result

class DocumentService:
    def __init__(self):
        self.db = MySQLDatabase("cskb")
    
    def get_docs_by_agent(self, agent_id: str):
        sql = """
        SELECT id, agent_id, title, plain_text, rich_text, 
               pub_user_name, pub_time, version_status
        FROM cskb_doc_pub 
        WHERE agent_id = %s AND delete_flag = 0
        """
        
        # 执行查询
        mysql_results = self.db.execute_query(sql, (agent_id,))
        
        # 使用映射器转换结果
        mapped_results = map_doc_result(mysql_results, use_dict_cursor=True)
        
        return mapped_results
```

## 支持的查询方式

### 1. DictCursor查询 (推荐)
```python
# 使用DictCursor，结果为字典格式
mapped_result = map_doc_result(dict_result, use_dict_cursor=True)
```

### 2. 传统Cursor查询
```python
# 使用传统Cursor，结果为元组格式
mapped_result = map_doc_result(tuple_result, use_dict_cursor=False)
```

## 虚拟字段处理

某些目标字段在MySQL表中不存在，需要额外查询：

- `agentName`: 需要通过 `agent_id` 查询agent表获得
- `dir_name`: 需要通过 `dir_id` 查询目录表获得  
- `dir_level`: 需要通过 `dir_id` 查询目录表获得
- `id_parent`: 虚拟字段，通常为空
- `originalId_parent`: 虚拟字段，通常为空

这些字段在映射后会被设置为 `None`，需要在业务层补充实际值。

## 错误处理

```python
try:
    mapped_result = map_doc_result(mysql_result, use_dict_cursor=True)
except TypeError as e:
    print(f"数据类型错误: {e}")
except Exception as e:
    print(f"映射失败: {e}")
```

## 扩展字段

如果需要添加新的字段映射：

1. 在 `DOC_FIELD_MAPPING` 或 `FAQ_FIELD_MAPPING` 中添加映射关系
2. 在 `DOC_TARGET_FIELDS` 或 `FAQ_TARGET_FIELDS` 中添加目标字段名
3. 确保SQL查询包含对应的MySQL字段

```python
# 示例：添加新字段映射
DOC_FIELD_MAPPING['new_mysql_field'] = 'newTargetField'
DOC_TARGET_FIELDS.append('newTargetField')
```

## 项目集成建议

1. **数据访问层**: 在所有数据库查询方法中使用映射器
2. **统一接口**: 确保所有对外接口返回标准格式的数据
3. **错误处理**: 添加适当的异常处理和日志记录
4. **性能优化**: 只查询需要的字段，避免 `SELECT *`
5. **测试覆盖**: 为映射逻辑编写单元测试

## 示例文件

- `examples/updated_mapper_usage.py`: 基础使用示例
- `examples/cskb_integration_example.py`: 项目集成示例

## 注意事项

1. 映射器只处理字段名转换，不处理数据类型转换
2. 缺失的字段会被设置为 `None`
3. 虚拟字段需要在业务层补充实际值
4. 建议使用DictCursor以获得更好的可读性和安全性
