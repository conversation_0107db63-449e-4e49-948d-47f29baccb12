import asyncio
from langchain.docstore.document import Document
from langchain.text_splitter import RecursiveCharacterTextSplitter, SpacyTextSplitter, NLTKTextSplitter

from env import ENVIRONMENT
from config import cskb_to_milvus_dict
from rag.embedding import upload_knowledge, batch_upload_knowledge, async_batch_upload_knowledge

def chunk_base_faq(
    faq_dict, chunk_size: int, chunk_method:str, chunk_overlap: int = 0
):
    faq_question = faq_dict['question']
    print (f'Chunking faq "{faq_question}" in {chunk_method} mode')
    
    chunked_faq = split_chunks(
        chunk_type = "cskb_faq",
        chunk_method = chunk_method,
        agent_id = faq_dict['agentId'],
        agent_name = faq_dict['agentName'],
        doc_id = faq_dict['id'],
        doc_id_parent= "",
        dir_id = faq_dict['dirId'], 
        chunk_name = faq_question,
        chunk_id = faq_dict['originalId'],
        chunk_id_parent = "",
        chunk_size = chunk_size,
        chunk_overlap = chunk_overlap,
        chunk_content = faq_dict['answer'],
        chunk_category = faq_dict['dir_name'],
    )
    
    print (f'Chunked doc "{faq_question}" in {chunk_method} mode with chunks: {len(chunked_faq)}')
    
    print ('chunked_faq', chunked_faq)
    
    return chunked_faq

    # for faq in chunked_faq:
    #     upload_knowledge(database=database, collection='faq', texts=[faq.page_content], metadata=faq.metadata)


# 文本分割具体步骤
def split_chunks(chunk_type, chunk_method, agent_id, agent_name, doc_id, doc_id_parent, dir_id,chunk_name, chunk_id, chunk_id_parent, chunk_size, chunk_overlap, chunk_content, chunk_category):
    chunked_docs = []

    if (chunk_type == "cskb_faq"):
        chunks = [chunk_name]
    else:
        # 重新组合chunk内容，使其同时包含知识标题和知识内容
        if (chunk_name != ""):
            new_chunk_content = "知识文档标题：" + chunk_name + "\n" + "知识文档内容：\n" + chunk_content
        else:
            new_chunk_content = chunk_content

        if chunk_method == "RCTS": 
            if str(chunk_name).find("FAQ") != -1:
                text_splitter = RecursiveCharacterTextSplitter(chunk_size = chunk_size, chunk_overlap = chunk_overlap, length_function = len, separators=['\n问题：',"\n## "])
            else:
                text_splitter = RecursiveCharacterTextSplitter(chunk_size = chunk_size, chunk_overlap = chunk_overlap, length_function = len, separators=["\n# "])
        elif chunk_method == "NLTK":
            text_splitter = NLTKTextSplitter(chunk_size=chunk_size, chunk_overlap = chunk_overlap, length_function = len)
        elif chunk_method == "Spacy":
            text_splitter = SpacyTextSplitter(chunk_size=chunk_size, chunk_overlap = chunk_overlap, length_function = len, pipeline="zh_core_web_sm", max_length=2500000)
        elif chunk_method == "SingleChunk":
            pass
        else:
            print ("Error in choosing chunk method")

        # TODO：如果chunk内容过大，超过100万字符，不进行chunking。未来考虑是否需要分块后再进行chunking
        if len(new_chunk_content) > 1000000:
            print ("Chunk content too large, skip chunking")
            return chunked_docs

        if chunk_method == "NLTK" or chunk_method == "SingleChunk":
            chunks = [new_chunk_content]
        else: 
            chunks = text_splitter.split_text(new_chunk_content)
    
    
    for i, chunk in enumerate(chunks):
        doc = Document(
            page_content=chunk.strip(),
            metadata={
                "type" : chunk_type,
                "agent_id": agent_id,
                "agent_name": agent_name,
                "doc_id": doc_id,
                "doc_id_parent": doc_id_parent,
                "source_id" : chunk_id,
                "source_id_parent": chunk_id_parent,
                "source" : chunk_name,
                "category" : chunk_category,
                "dirId" : dir_id,
                "chunk" : i + 1,
                "faq_answer": chunk_content.strip() if chunk_type == "cskb_faq" else ""
            },
        )
        chunked_docs.append(doc)
    
    return chunked_docs


# 百度知识库文档文本分割
def chunk_base_doc(doc_dict, chunk_size: int, chunk_method:str, chunk_overlap: int = 0, type:str = "doc"):
    doc_name = doc_dict['title']
    print (f'Chunking doc "{doc_name}" in {chunk_method} mode')

    chunk_type = "cskb_attach" if type == "attach" else "cskb_doc"

    chunked_docs = split_chunks(
        chunk_type = chunk_type,
        chunk_method = chunk_method,
        agent_id = doc_dict['agentId'],
        agent_name = doc_dict['agentName'],
        dir_id = doc_dict['dirId'],
        doc_id = doc_dict['id'],
        doc_id_parent = doc_dict['id_parent'],
        chunk_name = doc_name,
        chunk_id = doc_dict['originalId'],
        chunk_id_parent = doc_dict['originalId_parent'],
        chunk_size = chunk_size,
        chunk_overlap = chunk_overlap,
        chunk_content = doc_dict['docText'],
        chunk_category = doc_dict['dir_name'],
    )
    
    return chunked_docs
    # for doc in chunked_docs:
    #     print('doc', doc)
    #     print('doc', doc.metadata)
    #     upload_knowledge(database=database, collection='doc', texts=[doc.page_content], metadata=doc.metadata)
    #     return 
    
    # print (f'Chunked doc "{doc_name}" in {chunk_method} mode with chunks: {len(chunked_docs)}')

    # all_chunks = []
    # for doc in chunked_docs:
    #     all_chunks.append(doc)

    # print('----------------------------------------------')
    # print('all_chunks', all_chunks)
    # print('**********************************************')
    # return
  




# def load_and_chunk_base(agent: dict, knowledge_list: list):
#     chunk_list = []
    
#     milvus_database = 'CSKB_' + agent['agent_name_en']
#     for i,item in enumerate(knowledge_list):
#         if (item['type'] == 'faq'):
#             faq = item['data']
#             chunks = chunk_base_faq(milvus_database, faq, chunk_size=0, chunk_overlap=0, chunk_method="SingleChunk")
#         elif (item['type'] == 'attach'):
#             doc = item['data']
#             if "[项目知了知识库]" in doc['dir_name'] or "[智慧党建知识库]" in doc['dir_name']:
#                 chunks = chunk_base_doc(milvus_database, doc, chunk_size=0, chunk_overlap=0, chunk_method="SingleChunk", type="attach")
#             elif "[RiSE谈判助手知识库]" in doc['dir_name']:
#                 chunks = chunk_base_doc(milvus_database, doc, chunk_size=30000, chunk_overlap=10000, chunk_method="Spacy", type="attach")
#             else:
#                 chunks = chunk_base_doc(milvus_database, doc, chunk_size=200, chunk_overlap=100, chunk_method="Spacy", type="attach")
#         else:
#             # 此分支包含doc和attach两种type类型
#             doc = item['data']

#             if "[项目知了知识库]" in doc['dir_name'] or "[智慧党建知识库]" in doc['dir_name']:
#                 chunks = chunk_base_doc(milvus_database, doc, chunk_size=0, chunk_overlap=0, chunk_method="SingleChunk", type="doc")
#             elif "[RiSE谈判助手知识库]" in doc['dir_name']:
#                 chunks = chunk_base_doc(milvus_database, doc, chunk_size=0, chunk_overlap=0, chunk_method="SingleChunk", type="doc")
#             else:
#                 chunks = chunk_base_doc(milvus_database, doc, chunk_size=200, chunk_overlap=100, chunk_method="Spacy", type="doc")
#                 # break
        
#         chunk_list += chunks
        
#         if (i + 1) % 10 == 0 or i + 1 == len(knowledge_list):
#             print(f'Loaded Knowledge Item={i+1}/{len(knowledge_list)}, completed={round((i+1)/len(knowledge_list)*100,2)}%\n')
#     return chunk_list


def chunk_knowledge_by_type(item):
    # 原循环体内的处理逻辑
    if (item['type'] == 'faq'):
        faq = item['data']
        return chunk_base_faq(faq, chunk_size=0, chunk_overlap=0, chunk_method="SingleChunk")
    elif (item['type'] == 'attach'):
        doc = item['data']
        if "[项目知了知识库]" in doc['dir_name'] or "[智慧党建知识库]" in doc['dir_name']:
            return chunk_base_doc(doc, chunk_size=0, chunk_overlap=0, chunk_method="SingleChunk", type="attach")
        elif "[RiSE谈判助手知识库]" in doc['dir_name']:
            return chunk_base_doc(doc, chunk_size=30000, chunk_overlap=10000, chunk_method="Spacy", type="attach")
        else:
            return chunk_base_doc(doc, chunk_size=200, chunk_overlap=100, chunk_method="Spacy", type="attach")
    else:
        doc = item['data']
        if "[项目知了知识库]" in doc['dir_name'] or "[智慧党建知识库]" in doc['dir_name']:
            return chunk_base_doc(doc, chunk_size=0, chunk_overlap=0, chunk_method="SingleChunk", type="doc")
        elif "[RiSE谈判助手知识库]" in doc['dir_name']:
            return chunk_base_doc(doc, chunk_size=0, chunk_overlap=0, chunk_method="SingleChunk", type="doc")
        else:
            return chunk_base_doc(doc, chunk_size=200, chunk_overlap=100, chunk_method="Spacy", type="doc")



def load_and_chunk_base(agent: dict, knowledge_list: list):
    chunk_list = []
    completed_count = 0
    
    from concurrent.futures import ThreadPoolExecutor, as_completed
    
    with ThreadPoolExecutor(max_workers=8) as executor:
        futures = {executor.submit(chunk_knowledge_by_type, item): i for i, item in enumerate(knowledge_list)}
        
        for future in as_completed(futures):
            try:
                chunk_list.extend(future.result())
                completed_count += 1
                
                if completed_count % 10 == 0 or completed_count == len(knowledge_list):
                    print(f'Loaded Knowledge Item={completed_count}/{len(knowledge_list)}, completed={round(completed_count/len(knowledge_list)*100,2)}%\n')
            except Exception as e:
                print(f"处理失败: {e}")
                completed_count += 1

    return chunk_list



async def optimized_upload_strategy(agent: dict, knowledge_list: list):
    """异步并行上传策略（支持失败重试）"""
    
    # 1. 全量分块（保持当前逻辑）
    chunk_list = load_and_chunk_base(agent, knowledge_list)
    
    # 2. 并行上传参数配置
    DATABASE = 'CSKB_' + agent['agent_name_en']
    MAX_CONCURRENT_UPLOADS = 8  # 并行上传任务数
    BATCH_SIZE = 100  # 单次请求批次大小
    
    # 3. 按类型分组，减少索引切换
    from collections import defaultdict
    type_batches = defaultdict(list)
    
    for chunk in chunk_list:
        collection = chunk.metadata['type'].split('_')[1]
        type_batches[collection].append({
            "text": chunk.page_content,
            "metadata": chunk.metadata
        })
    

    async def _upload_batch(collection: str, items: list):
        """带重试机制的单个批次上传"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                result = await async_batch_upload_knowledge(
                    items=items,
                    collection=collection,
                    database=DATABASE,
                    batch_size=BATCH_SIZE
                )
                print(f"✓ 成功上传 {collection}: {len(items)} 条")
                return result
            except Exception as e:
                if attempt == max_retries - 1:
                    print(f"× 上传失败 {collection}: {str(e)}")
                    raise
                await asyncio.sleep(2 ** attempt)
    
    # 创建所有上传任务
    tasks = []
    for collection, items in type_batches.items():
        # 拆分超大请求为批次
        for i in range(0, len(items), BATCH_SIZE):
            batch = items[i:i+BATCH_SIZE]
            task = _upload_batch(collection, batch)
            tasks.append(task)
    
    # 使用信号量控制并发数
    semaphore = asyncio.Semaphore(MAX_CONCURRENT_UPLOADS)
    async with semaphore:
        results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 统计上传结果
    success_count = sum(1 for r in results if not isinstance(r, Exception))
    print(f"上传完成！成功批次: {success_count}/{len(tasks)}")



def upload_chunk(agent: dict, chunk_list: list):
    database = 'CSKB_' + agent['agent_name_en']
    BATCH_SIZE = 50  # 根据向量库接口承受能力调整
    for chunk_type in ['cskb_attach', 'cskb_doc', 'cskb_faq']:
        items = [{"text": chunk.page_content,"metadata": chunk.metadata} for chunk in chunk_list if chunk.metadata['type'] == chunk_type]
        collection = chunk_type.split('_')[1]
        if items:
            # 分批处理
            for i in range(0, len(items), BATCH_SIZE):
                batch = items[i:i+BATCH_SIZE]
                collection = chunk_type.split('_')[1]
                batch_upload_knowledge(items=batch, collection=collection, database=database)


if __name__ == '__main__':
    data = {'id': 'c2_8a77f2a5c5e15071301b69f0e5cfb5c5', 'agentId': 'ba96968f61b6b72a6ef97c87ab99bee6', 'agentName': '零号员工知识库', 'question': '部门如何进行会务接待礼仪服务申请？会务礼仪申请？', 'answer': '1.公司及以上级别的会议、接待、大型活动中，主办方如有会务服务需求，可申请会务服务。2.会务服务包含会场布置、茶水服务、会议所涉及的礼仪服务等。3.主办部门提前一周填写《HSC会务接待礼仪服务申请表》，完成审批流转后，联系HSC俞华695-52665，或IMB会务52653进行具体需求的对接。', 'dirId': 'a0f9bb4bab23c16a41ada082883b5cb0', 'originalId': 'a2_2f2414f00ee96dbf877cd65e61d4ce0f', 'pubUserName': '曹阳_67841', 'pubTime': '2025-03-31 11:02:39', 'versionStatus': 1, 'dir_name': '[零号员工知识库] HS-安全管理知识', 'dir_level': 1}
    # upload_knowledge(data)