import json
import requests
from typing import Dict, Any, Optional


class HttpClient:
    """通用的HTTP请求客户端"""

    @staticmethod
    def make_request(
        url: str,
        headers: Dict[str, str],
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        method: str = 'GET',
        timeout: int = 30
    ) -> Dict[str, Any]:
        """
        统一的HTTP请求方法

        Args:
            url: 请求URL
            headers: 请求头
            params: URL参数（用于GET请求）
            data: 请求体数据（用于POST请求）
            method: HTTP方法 ('GET', 'POST', 'PUT', 'DELETE')
            timeout: 超时时间（秒）

        Returns:
            响应的JSON数据，如果失败返回空字典
        """
        try:
            method = method.upper()

            if method == 'GET':
                response = requests.get(url, headers=headers, params=params, timeout=timeout)
            elif method == 'POST':
                response = requests.post(url, headers=headers, json=data, params=params, timeout=timeout)
            elif method == 'PUT':
                response = requests.put(url, headers=headers, json=data, params=params, timeout=timeout)
            elif method == 'DELETE':
                response = requests.delete(url, headers=headers, params=params, timeout=timeout)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            response.raise_for_status()  # 抛出HTTP错误

            # 尝试解析JSON，如果失败则返回文本内容
            try:
                return response.json()
            except json.JSONDecodeError:
                return {"text": response.text, "status_code": response.status_code}

        except requests.exceptions.Timeout:
            print(f"请求超时: {url}")
            return {"error": "timeout", "url": url}
        except requests.exceptions.ConnectionError:
            print(f"连接错误: {url}")
            return {"error": "connection_error", "url": url}
        except requests.exceptions.HTTPError as e:
            print(f"HTTP错误: {url}, 状态码: {e.response.status_code}")
            return {"error": "http_error", "status_code": e.response.status_code, "url": url}
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {url}, 错误: {e}")
            return {"error": "request_failed", "message": str(e), "url": url}
        except Exception as e:
            print(f"未知错误: {url}, 错误: {e}")
            return {"error": "unknown_error", "message": str(e), "url": url}

    @staticmethod
    def build_headers(
        content_type: str = "application/json",
        charset: str = "utf-8",
        authorization: Optional[str] = None,
        **kwargs
    ) -> Dict[str, str]:
        """
        构建通用的请求头

        Args:
            content_type: 内容类型
            charset: 字符编码
            authorization: 授权token
            **kwargs: 其他自定义头部

        Returns:
            请求头字典
        """
        headers = {
            "Content-Type": content_type,
            "charset": charset,
        }

        if authorization:
            headers["Authorization"] = authorization

        # 添加其他自定义头部
        headers.update(kwargs)

        return headers

    @staticmethod
    def paginated_request(
        base_url: str,
        headers: Dict[str, str],
        page_param: str = "pn",
        size_param: str = "ps",
        page_size: int = 100,
        max_pages: int = 9999,
        start_page: int = 1,
        **kwargs
    ) -> list:
        """
        通用的分页请求方法

        Args:
            base_url: 基础URL（不包含分页参数）
            headers: 请求头
            page_param: 页码参数名
            size_param: 页大小参数名
            page_size: 每页大小
            max_pages: 最大页数
            start_page: 起始页码
            **kwargs: 其他URL参数

        Returns:
            所有页面的数据列表
        """
        all_results = []
        page = start_page

        while page <= max_pages:
            # 构建分页参数
            params = {
                page_param: page,
                size_param: page_size,
                **kwargs
            }

            # 构建完整URL
            if '?' in base_url:
                url = base_url + '&' + '&'.join([f"{k}={v}" for k, v in params.items()])
            else:
                url = base_url + '?' + '&'.join([f"{k}={v}" for k, v in params.items()])

            response_data = HttpClient.make_request(url, headers)

            # 检查是否有错误
            if "error" in response_data:
                print(f"分页请求失败: 第{page}页, 错误: {response_data.get('error')}")
                break

            # 检查是否有数据
            if "data" not in response_data:
                break

            page_results = response_data['data'].get('list', [])
            if not page_results:
                break

            all_results.extend(page_results)
            page += 1

        return all_results