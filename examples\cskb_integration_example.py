"""
CSKB操作类集成数据映射器示例
展示如何在实际的数据访问层中使用数据映射器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.mysql import MySQLDatabase
from utils.data_mapper import map_doc_result, map_faq_result
from config import ENVIRONMENT


class CSKBOperationWithMapper:
    """
    集成了数据映射器的CSKB操作类示例
    """
    
    def __init__(self, db: str = 'cskb'):
        self.db = MySQLDatabase(db)
        self.env = ENVIRONMENT
    
    def get_docs_by_agent(self, agent_id: str, limit: int = 10) -> list:
        """
        根据agent_id获取文档列表，返回标准格式的数据
        
        Args:
            agent_id: 智能体ID
            limit: 返回记录数限制
        
        Returns:
            标准格式的文档列表
        """
        # 构建SQL查询（只查询需要的字段）
        sql = """
        SELECT 
            id,
            agent_id,
            title,
            plain_text,
            rich_text,
            dir_id,
            pub_user_name,
            pub_time,
            version,
            tags,
            attaches,
            word_num,
            picture_num,
            link_num,
            original_id,
            version_status,
            sys_created,
            sys_updated
        FROM cskb_doc_pub 
        WHERE agent_id = %s 
        AND version_status = 1 
        AND delete_flag = 0
        ORDER BY pub_time DESC
        LIMIT %s
        """
        
        try:
            # 执行查询
            mysql_results = self.db.execute_query(sql, (agent_id, limit))
            
            # 使用数据映射器转换结果
            mapped_results = map_doc_result(mysql_results, use_dict_cursor=True)

            # 确保返回列表类型
            if isinstance(mapped_results, list):
                return mapped_results
            else:
                return [mapped_results] if mapped_results else []
            
        except Exception as e:
            print(f"查询文档失败: {e}")
            return []
    
    def get_faqs_by_agent(self, agent_id: str, limit: int = 10) -> list:
        """
        根据agent_id获取FAQ列表，返回标准格式的数据
        
        Args:
            agent_id: 智能体ID
            limit: 返回记录数限制
        
        Returns:
            标准格式的FAQ列表
        """
        sql = """
        SELECT 
            id,
            agent_id,
            question,
            answer,
            dir_id,
            original_id,
            pub_user_name,
            pub_time,
            version_status,
            tags,
            version,
            sys_created,
            sys_updated
        FROM cskb_faq_pub 
        WHERE agent_id = %s 
        AND version_status = 1 
        AND delete_flag = 0
        ORDER BY pub_time DESC
        LIMIT %s
        """
        
        try:
            mysql_results = self.db.execute_query(sql, (agent_id, limit))
            mapped_results = map_faq_result(mysql_results, use_dict_cursor=True)
            # 确保返回列表类型
            if isinstance(mapped_results, list):
                return mapped_results
            else:
                return [mapped_results] if mapped_results else []
            
        except Exception as e:
            print(f"查询FAQ失败: {e}")
            return []
    
    def get_doc_by_id(self, doc_id: str) -> dict:
        """
        根据文档ID获取单个文档的详细信息
        
        Args:
            doc_id: 文档ID
        
        Returns:
            标准格式的文档字典，如果未找到返回空字典
        """
        sql = """
        SELECT 
            id,
            agent_id,
            title,
            plain_text,
            rich_text,
            dir_id,
            pub_user_name,
            pub_time,
            version,
            tags,
            attaches,
            word_num,
            picture_num,
            link_num,
            original_id,
            version_status,
            sys_created,
            sys_updated,
            created_user_name,
            last_edit_user_name,
            last_edit_time
        FROM cskb_doc_pub 
        WHERE id = %s 
        AND delete_flag = 0
        """
        
        try:
            mysql_results = self.db.execute_query(sql, (doc_id,))
            
            if mysql_results:
                # 单个结果
                mapped_result = map_doc_result(mysql_results[0], use_dict_cursor=True)
                # 确保返回字典类型
                if isinstance(mapped_result, dict):
                    return mapped_result
                elif isinstance(mapped_result, list) and mapped_result:
                    return mapped_result[0]
                else:
                    return {}
            else:
                return {}
                
        except Exception as e:
            print(f"查询文档详情失败: {e}")
            return {}
    
    def get_incremental_docs(self, agent_id: str, query_date: str) -> list:
        """
        获取增量更新的文档
        
        Args:
            agent_id: 智能体ID
            query_date: 查询日期 (YYYY-MM-DD)
        
        Returns:
            标准格式的文档列表
        """
        sql = """
        SELECT 
            id,
            agent_id,
            title,
            plain_text,
            rich_text,
            dir_id,
            pub_user_name,
            pub_time,
            version,
            original_id,
            version_status,
            sys_updated
        FROM cskb_doc_pub 
        WHERE agent_id = %s 
        AND version_status = 1 
        AND DATE(sys_updated) = %s
        AND delete_flag = 0
        ORDER BY sys_updated DESC
        """
        
        try:
            mysql_results = self.db.execute_query(sql, (agent_id, query_date))
            mapped_results = map_doc_result(mysql_results, use_dict_cursor=True)
            # 确保返回列表类型
            if isinstance(mapped_results, list):
                return mapped_results
            else:
                return [mapped_results] if mapped_results else []
            
        except Exception as e:
            print(f"查询增量文档失败: {e}")
            return []
    
    def search_docs_by_keyword(self, agent_id: str, keyword: str, limit: int = 20) -> list:
        """
        根据关键词搜索文档
        
        Args:
            agent_id: 智能体ID
            keyword: 搜索关键词
            limit: 返回记录数限制
        
        Returns:
            标准格式的文档列表
        """
        sql = """
        SELECT 
            id,
            agent_id,
            title,
            plain_text,
            dir_id,
            pub_user_name,
            pub_time,
            version,
            tags,
            word_num,
            original_id,
            version_status
        FROM cskb_doc_pub 
        WHERE agent_id = %s 
        AND version_status = 1 
        AND delete_flag = 0
        AND (title LIKE %s OR plain_text LIKE %s OR tags LIKE %s)
        ORDER BY pub_time DESC
        LIMIT %s
        """
        
        keyword_pattern = f"%{keyword}%"
        
        try:
            mysql_results = self.db.execute_query(
                sql, 
                (agent_id, keyword_pattern, keyword_pattern, keyword_pattern, limit)
            )
            mapped_results = map_doc_result(mysql_results, use_dict_cursor=True)
            # 确保返回列表类型
            if isinstance(mapped_results, list):
                return mapped_results
            else:
                return [mapped_results] if mapped_results else []
            
        except Exception as e:
            print(f"搜索文档失败: {e}")
            return []
    
    def close(self):
        """关闭数据库连接"""
        if self.db:
            self.db.close()


def demo_usage():
    """
    演示如何使用集成了映射器的CSKB操作类
    """
    print("=== CSKB操作类集成映射器演示 ===")
    
    try:
        # 创建操作实例
        cskb_op = CSKBOperationWithMapper()
        
        # 示例1: 获取指定agent的文档
        print("1. 获取agent文档列表:")
        docs = cskb_op.get_docs_by_agent("test_agent_id", limit=3)
        print(f"   查询到 {len(docs)} 条文档记录")
        
        if docs:
            print("   第一条文档信息:")
            doc = docs[0]
            print(f"     id: {doc.get('id')}")
            print(f"     title: {doc.get('title')}")
            print(f"     agentId: {doc.get('agentId')}")  # 注意字段名已转换
            print(f"     pubUserName: {doc.get('pubUserName')}")
            print(f"     wordNum: {doc.get('wordNum')}")
        
        # 示例2: 获取FAQ列表
        print("\n2. 获取FAQ列表:")
        faqs = cskb_op.get_faqs_by_agent("test_agent_id", limit=2)
        print(f"   查询到 {len(faqs)} 条FAQ记录")
        
        # 示例3: 搜索文档
        print("\n3. 搜索文档:")
        search_results = cskb_op.search_docs_by_keyword("test_agent_id", "用户", limit=5)
        print(f"   搜索到 {len(search_results)} 条相关文档")
        
        # 关闭连接
        cskb_op.close()
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")


if __name__ == "__main__":
    demo_usage()
    
    print("\n" + "="*60)
    print("集成建议:")
    print("""
    1. 在现有的cskb_operation.py中导入映射器:
       from utils.data_mapper import map_doc_result, map_faq_result
    
    2. 在查询方法中使用映射器转换结果:
       mysql_results = self.db.execute_query(sql, params)
       return map_doc_result(mysql_results, use_dict_cursor=True)
    
    3. 根据需要选择字段范围:
       - 列表查询: use_full_fields=False (只返回核心字段)
       - 详情查询: use_full_fields=True (返回完整字段)
    
    4. 处理虚拟字段:
       - agentName: 通过agent_id查询agent表获得
       - dir_name, dir_level: 通过dir_id查询目录表获得
    
    5. 错误处理:
       - 在映射前检查查询结果是否为空
       - 捕获映射过程中的类型错误
    """)
