import json
import requests
import asyncio
import aiohttp
import time
from typing import List, Dict, Optional

import os
import sys
# 添加上上层目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from config import get_service_url


base_url = get_service_url("rag_service")

def upload_knowledge(database:str, collection:str, texts:list, metadata:dict):

    print("调用文本列表上传接口...")
    url = f"{base_url}/upload_texts"

    # upload_data
    upload_data = {
        "texts": texts,
        "database": database,
        "collection": collection,
        "metadata": metadata
    }
 
    # 发送请求
    try:
        response = requests.post(url, json=upload_data)
        
        # 打印响应
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            print(f"成功上传 {result.get('text_count', 0)} 条文本")
            print(f"文档ID: {result.get('doc_id', '')}")
            print(f"向量维度: {result.get('vector_dimension', 0)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求出错: {e}")
    
    print("测试完成")


def batch_upload_knowledge(items: list, collection: str, database: str, encrypt: bool = False, embedding_type: str = "huggingface", batch_size: int = 5):
    print("调用批量文本上传接口...")
    base_url = get_service_url("rag_service")
    print(base_url)
    url = f"{base_url}/upload_texts_batch"

    # upload_data
    upload_data = {
        "items": items,
        "collection": collection,
        "database": database,
        "encrypt": encrypt,
        "embedding_type": embedding_type,
        "batch_size": batch_size
    }
 
    # 发送请求
    try:
        response = requests.post(url, json=upload_data)
        
        # 打印响应
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            print(f"成功上传 {result.get('text_count', 0)} 条文本")
            print(f"文档ID: {result.get('doc_id', '')}")
            print(f"向量维度: {result.get('vector_dimension', 0)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求出错: {e}")
    
    print("测试完成")


async def async_batch_upload_knowledge(
    items: List[Dict],
    collection: str,
    database: str,
    encrypt: bool = False,
    embedding_type: str = "huggingface",
    batch_size: int = 5,
    timeout: int = 300
) -> Dict:
    """
    简化版异步批量上传（可直接替换同步版本）
    """
    base_url = get_service_url("rag_service")
    url = f"{base_url}/upload_texts_batch"
    
    upload_data = {
        "items": items,
        "collection": collection,
        "database": database,
        "encrypt": encrypt,
        "embedding_type": embedding_type,
        "batch_size": batch_size
    }
    
    timeout_config = aiohttp.ClientTimeout(total=timeout)
    
    async with aiohttp.ClientSession(timeout=timeout_config) as session:
        async with session.post(url, json=upload_data) as response:
            if response.status == 200:
                result = await response.json()
                print(f"✓ 成功上传 {len(items)} 条文本到 {collection}")
                return result
            else:
                error_text = await response.text()
                raise Exception(f"上传失败 HTTP {response.status}: {error_text}")


# 按ID删除示例
def delete_by_ids(database: str, collection: str, ids: list, auto_flush: bool = False):
    print("调用按ID删除接口...")
    url = f"{base_url}/delete"
    payload = {
        "database": database,
        "collection": collection,
        "ids": ids,
        "auto_flush": auto_flush
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        print("删除成功:", response.json())
    except requests.exceptions.HTTPError as err:
        print(f"HTTP错误: {err}")
        print("响应内容:", err.response.text)
    except Exception as e:
        print(f"其他错误: {str(e)}")

# 按条件删除
def delete_by_condition(database: str, collection: str, filter_expr: str, auto_flush: bool = False):
    print("调用条件删除接口...")
    url = f"{base_url}/delete"
        
    # # 构建metadata筛选表达式
    # quoted_ids = [f"'{doc_id}'" for doc_id in doc_ids]
    # filter_expr = f"metadata['doc_id'] in [{','.join(quoted_ids)}]"
    
    payload = {
        "database": database,
        "collection": collection,
        "filter_expr": filter_expr,
        "auto_flush": auto_flush
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        print("删除成功:", response.json())
    except requests.exceptions.HTTPError as err:
        print(f"HTTP错误: {err}")
        print("响应内容:", err.response.text)
    except Exception as e:
        print(f"其他错误: {str(e)}")


def update_single_record(database: str, collection: str, records: list, auto_flush: bool = False):
    """更新单条向量记录的示例"""
    
    url = f"{base_url}/upsert"
    # 要更新的数据
    update_data = {
        "records": records,
        "collection": collection,
        "database": database,
        "auto_flush": auto_flush 
    }
    
    # 调用upsert接口
    response = requests.post(
        url,
        json=update_data,
        headers={"Content-Type": "application/json"}
    )
    
    return response.json()


# def update_embedding(database: str, collection: str, records: list, action: str, embedding_type: str, encrypt: bool = False):
#     """更新向量数据库数据"""
#     if action == 'add':
#         batch_upload_knowledge(items=records, collection=collection, database=database, embedding_type=embedding_type, encrypt=encrypt)
#     elif action == 'update':
#         update_single_record(database, collection, records, auto_flush=True)
#     elif action == 'delete':
#         delete_by_ids(database, collection, records)
    





if __name__ == '__main__':
    # items = [
    #     {
    #         "text": "部门如何进行会务接待礼仪服务申请？会务礼仪申请？",
    #         "metadata": {
    #             "type": "cskb_faq",
    #             "category": "会务服务",
    #             "sub_category": "会务接待",
    #             "source": "部门文档",
    #             "source_type": "部门文档",
    #             "source_id": "123456",
    #             "agent_id": "123456",
    #             "agent_name": "部门名称",
    #             "doc_id": "123456",
    #             "doc_id_parent": "",
    #             "dir_id": "123456",
    #             "chunk": 1, 
    #             "faq_answer": "1.公司及以上级别的会议、接待、大型活动中，主办方如有会务服务需求，可申请会务服务。"
    #         }
    #     }
    # ]
    collection = "faq_test"
    database = "EmpZero"
    # batch_upload_knowledge(items, collection, database)
    # delete_by_ids(database, collection, [458603706594809451, 458603706594809453])
    records = [
            {
                "id": 458603706594809461, 
                "content": "这是一个",
                "metadata": {
                    "category": "technology",
                    "author": "张三",
                    "created_date": "2024-01-15",
                    "version": 2
                }
            }
        ]
    results = update_single_record(database, collection, records)
    print(results)


