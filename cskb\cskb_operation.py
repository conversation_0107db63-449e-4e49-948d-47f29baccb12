import re
import fitz
import json
import datetime
import requests
from tqdm import tqdm
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor

import os
import sys
# 添加上上层目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from database.mysql import MySQLDatabase
from config import CSKB_ADDRESS_DICT
from env import ENVIRONMENT
from utils.data_mapper import map_doc_result, map_faq_result, get_doc_sql_fields, get_faq_sql_fields
from utils.utils import html_to_text
from utils.request import HttpClient
from rag.embedding import delete_by_condition
from rag.chunking import load_and_chunk_base, upload_chunk




class CSKBOperation:
    def __init__(self, db: str = 'cskb'):
        self.db = MySQLDatabase(db)
        self.env = ENVIRONMENT
        self.all_dir_tree_dict = {}

    def _build_headers(self, access_token: str) -> dict:
        """构建CSKB专用的请求头"""
        headers = HttpClient.build_headers(authorization=access_token)
        headers['X-USER-ID'] = 'COPILOT'
        return headers

    def _build_cskb_url(self, endpoint: str) -> str:
        """构建CSKB API URL"""
        cskb_url = CSKB_ADDRESS_DICT[self.env]['cskb_url']
        return f"{cskb_url}{endpoint}"

    def _make_request(self, url: str, headers: dict, params: dict = None, method: str = 'GET') -> dict:
        """统一的HTTP请求方法"""
        return HttpClient.make_request(url, headers, params=params, method=method)

    def _paginated_request(self, agent: dict, endpoint: str, page_size: int = 100, max_pages: int = 9999) -> list:
        """通用的分页请求方法"""
        headers = self._build_headers(agent['access_token'])
        base_url = self._build_cskb_url(endpoint)
        return HttpClient.paginated_request(
            base_url=base_url,
            headers=headers,
            page_size=page_size,
            max_pages=max_pages
        )


    # 查询知识库文档具体内容
    def get_agent_doc_detail(self, agent, doc_id):
        headers = self._build_headers(agent['access_token'])
        url = self._build_cskb_url(f"/cskb/open/api/v1/doc/detail?id={doc_id}")

        answer_json = self._make_request(url, headers)

        if "data" not in answer_json:
            return None

        data = answer_json['data']
        html_answer = data['richText']
        text_answer = html_to_text(html_answer)

        agent_name = agent['agent_name']

        doc_info = {
            "id": data['id'],
            "id_parent": "",
            "title": data['title'],
            "agentId": data['agentId'],
            "agentName": agent_name,
            "dirId": data['dirId'],
            "originalId": data['originalId'],
            "originalId_parent": "",
            "pubUserName": data['pubUserName'],
            "pubTime": data['pubTime'],
            "version": data['version'],
            "tags": data['tags'],
            "attaches": data['attaches'],
            "wordNum": data['wordNum'],
            "pictureNum": data['pictureNum'],
            "linkNum": data['linkNum'],
            "docText": text_answer,
            "docHtml": html_answer,
            "editType": data['editType']
        }

        return doc_info

    # 读取附件
    def get_agent_doc_attach_detail(self, agent, doc_title, attach_title, attach_id):
        headers = self._build_headers(agent['access_token'])
        cskb_attach_download_url = CSKB_ADDRESS_DICT[self.env]['cskb_attach_download_url']

        attach_content = f"BM/询价单名称：{doc_title}，附件名称：{attach_title}，附件内容如下：\n\n"

        attach_download_url = f"{cskb_attach_download_url}/cskb/storage/v1/download"
        param = {
            "id":attach_id,
            "preview": "true"
        }

        try:
            response = requests.get(attach_download_url, headers=headers, params=param)
        except Exception as error:
            print(f"附件{attach_id}下载失败: {error}")
            return ""
        
        if response.status_code == 200:
            # 获取内容类型
            content_type = response.headers.get('Content-Type', '')

            # 处理 PDF 响应
            if 'application/pdf' in content_type:
                try:
                    pdf = fitz.open(stream=response.content, filetype="pdf")  # type: ignore

                    for page in pdf:
                        text = page.get_text(sort=True)
                        text = re.sub(r"\s*\n\s*", "\n", text)
                        # @TODO:超过10000字符的部分不读取
                        if len(attach_content) > 1000000:
                            break
                        attach_content += text.strip()
                except Exception as error:
                    print(f"附件{attach_id}读取失败: {error}")

                finally:
                    pass
                
            # 处理图片响应
            elif 'image/' in content_type:
                # 暂时不对图片文件进行处理
                pass

            else:
                print(f"附件id:{attach_id}, 响应内容不是 PDF 或图片")
        else:
            print(f"附件id:{attach_id}, 请求失败，状态码: {response.status_code}")

        return attach_content

    def _ensure_attaches_is_list(self, doc):
        """确保文档的attaches字段是列表格式"""
        if 'attaches' in doc:
            attaches = doc['attaches']
            if isinstance(attaches, str) and attaches.strip():
                try:
                    doc['attaches'] = json.loads(attaches)
                except (json.JSONDecodeError, ValueError) as e:
                    print(f"附件字段JSON解析失败: {e}, 原值: {attaches}")
                    doc['attaches'] = []
            elif not isinstance(attaches, list):
                doc['attaches'] = []

    def _ensure_tags_is_list(self, doc):
        """确保文档的tags字段是列表格式"""
        if 'tags' in doc:
            tags = doc['tags']
            if isinstance(tags, str) and tags.strip():
                try:
                    doc['tags'] = json.loads(tags)
                except (json.JSONDecodeError, ValueError) as e:
                    print(f"标签字段JSON解析失败: {e}, 原值: {tags}")
                    doc['tags'] = []
            elif not isinstance(tags, list):
                doc['tags'] = []

    def _enrich_document_with_directory_info(self, doc, dir_tree_dict, agent_name):
        """为文档添加目录信息"""
        doc['agentName'] = agent_name
        doc_dir_id = doc['dirId']
        doc['dir_name'] = f"[{agent_name}] {dir_tree_dict[doc_dir_id]['full_name']}"
        doc['dir_level'] = dir_tree_dict[doc_dir_id]['level']
        doc['id_parent'] = ""
        doc["originalId_parent"] = ""

    def _process_attachment(self, attach, doc, agent):
        """处理单个附件"""
        # 附件文档中部分key，用其他key值替换
        attach['originalId'] = attach['id']
        attach['title'] = attach['name']

        # 附件的基本信息和其关联主文档保持一致
        attach['agentId'] = doc['agentId']
        attach['agentName'] = doc['agentName']
        attach['dirId'] = doc['dirId']
        attach['dir_name'] = doc['dir_name']
        attach['dir_level'] = doc['dir_level']

        # 获取附件内容
        attach_text = self.get_agent_doc_attach_detail(
            agent=agent,
            doc_title=doc['title'],
            attach_title=attach['title'],
            attach_id=attach['previewId']
        )
        attach['docText'] = attach_text
        attach["id_parent"] = doc['id']
        attach['originalId_parent'] = doc['originalId']

        return attach

    def _process_document_with_attachments(self, doc, agent, dir_tree_dict):
        """处理单个文档及其附件的通用方法"""
        knowledge_list = []

        # 确保JSON字段格式正确
        self._ensure_attaches_is_list(doc)
        self._ensure_tags_is_list(doc)

        # 验证文档内容
        doc_text = doc.get('docText', '')
        doc_html = doc.get('docHtml', '')
        doc_attaches = doc.get('attaches', [])

        if not doc_text and not doc_html and not doc_attaches:
            return knowledge_list

        # 添加目录信息
        self._enrich_document_with_directory_info(doc, dir_tree_dict, agent['agent_name'])
        
        if doc.get('editType') == 1:
            action = "add"
        elif doc.get('editType') == 2:
            action = "update"
        else:
            action = "other"
            
        # 添加文档到知识列表
        knowledge_list.append({"type": "doc", "data": doc, "action": action})

        # 处理附件
        if doc_attaches:
            # print('doc_attaches', doc_attaches)
            for attach in doc_attaches:
                # print('attach', attach)
                processed_attach = self._process_attachment(attach, doc, agent)
                knowledge_list.append({"type": "attach", "data": processed_attach, "action": action})

        return knowledge_list

    def get_agent_docs_and_attaches(self, agent):
        """根据文档id查询文档内容"""

        print('开始获取文档和附件')

        docs = self.get_agent_all_doc_ids(agent)
        # docs = [{'id': 'c1_c56a98c4b926dfc919ab9a736365d848', 'originalId': 'a1_95624089309d7b9d004425d4e05bf1d0'}]

        knowledge_list = []
        # 获取Agent目录清单
        dir_tree_dict = self.get_directory_tree(agent)

        # 使用线程池并行获取文档详情
        with ThreadPoolExecutor(max_workers=8) as executor:
            # 提交所有文档获取任务
            futures = {executor.submit(self.get_agent_doc_detail, agent=agent, doc_id=doc['id']): doc for doc in docs}
            
            # 使用新的进度条跟踪并发任务
            with tqdm(total=len(docs), desc=f"并发获取 {agent['agent_name']}", ncols=100) as con_progress:
                for future in concurrent.futures.as_completed(futures):
                    doc = futures[future]
                    doc_detail = future.result()
                    con_progress.update(1)
                    
                    if not doc_detail:
                        continue
                    
                    # 合并文档详情到原始数据
                    doc.update(doc_detail)
                    doc_knowledge_list = self._process_document_with_attachments(doc, agent, dir_tree_dict)
                    knowledge_list.extend(doc_knowledge_list)



        # 初始化带进度条的迭代器
        progress = tqdm(
            docs,
            desc=f"处理 {agent['agent_name']}",
            bar_format="{l_bar}{bar:20}{r_bar}",
            ncols=100,  # 控制进度条总宽度
            mininterval=0.5  # 更新频率
        )

        for doc in progress:
            doc_id = doc['id']
            doc_detail = self.get_agent_doc_detail(agent=agent, doc_id=doc_id)
            if not doc_detail:
                continue

            # 将doc_detail的内容更新到doc中
            doc.update(doc_detail)

            # 使用通用方法处理文档和附件
            doc_knowledge_list = self._process_document_with_attachments(doc, agent, dir_tree_dict)
            knowledge_list.extend(doc_knowledge_list)
            
            # break

        return knowledge_list


    # 获取所有文档
    def get_agent_all_doc_ids(self, agent):
        results = self._paginated_request(agent, "/cskb/open/api/v1/doc")

        docs_list = []
        for ans in results:
            doc_info = {
                "id": ans['id'],
                "originalId": ans['originalId'],
            }
            docs_list.append(doc_info)

        return docs_list

    def get_agent_all_knowledge(self, agent):
        docs_list = self.get_agent_docs_and_attaches(agent)
        faqs_list = self.get_agent_all_faqs(agent)
        faqs_list = []
        knowledge_list = docs_list + faqs_list
        return knowledge_list

    # 获取所有FAQ
    def get_agent_all_faqs(self, agent):
        print('开始获取faq')

        faq_list = []

        # 获取Agent目录清单
        dir_tree_dict = self.get_directory_tree(agent)

        results = self._paginated_request(agent, "/cskb/open/api/v1/faq/standard", max_pages=999)

        for ans in results:
            agent_id = ans['agentId']
            agent_name = agent['agent_name']

            question_text = ans['question']
            answer_json = json.loads(ans['answer'])['list'][0]
            if answer_json['type'] == 3:
                answer_text_html = answer_json['text']
                answer_text = re.sub('<.*?>', '', answer_text_html).strip()
            else:
                answer_text = answer_json['text']

            faq_info = {
                "id": ans['id'],
                "agentId": agent_id,
                "agentName": agent_name,
                "question": question_text,
                "answer": answer_text,
                "dirId": ans['dirId'],
                "originalId": ans['originalId'],
                "pubUserName": ans['pubUserName'],
                "pubTime": ans['pubTime'],
                "versionStatus": ans['versionStatus'],
                "editType": ans['editType'],
                "tags": ans['tags']
            }

            faq_dir_id = faq_info['dirId']
            faq_info['dir_name'] = f"[{faq_info['agentName']}] {dir_tree_dict[faq_dir_id]['full_name']}"
            faq_info['dir_level'] = dir_tree_dict[faq_dir_id]['level']

            faq_list.append({"type": "faq", "data": faq_info})

            # break  # 保留原有的break逻辑

        return faq_list


    # 获取目录结构
    def get_directory_tree(self, agent):
        headers = self._build_headers(agent['access_token'])
        url = self._build_cskb_url("/cskb/open/api/v1/directory?type=1")

        answer_dict = {}

        response_data = self._make_request(url, headers)
        if 'data' not in response_data:
            return answer_dict

        answer = response_data['data']

        # 将answer中完整目录树List转化成Dictionary，Key为dir_id，Value为完整目录名
        ## 第一步：创建一个临时Dictionary，Key为dir_id，Value为{底层目录名称，父目录ID，完整目录名称（由第二步逐层补充完整）}
        temp_dir_dict = {}
        for dir in answer:
            dir_id = dir['id']
            dir_name = dir['name']
            dir_parent_id = dir['parentId']
            temp_dir_dict[dir_id] = {"name": dir_name, "parent_id": dir_parent_id, "full_dir_name": dir_name}
        ## 第二步：逐层补充完整目录名称，并保存到一个global Dictionary中，Key为dir_id，Value为完整目录名称
        for dir in answer:
            dir_id = dir['id']
            dir_name = dir['name']
            dir_level = dir['level']
            dir_parent_id = dir['parentId']
            dir_full_name = dir_name
            while (dir_level > 1):
                dir_parent_name = temp_dir_dict[dir_parent_id]['full_dir_name']
                dir_full_name = f"{dir_parent_name}/" + dir_full_name
                dir_parent_id = temp_dir_dict[dir_parent_id]['parent_id']
                dir_level -= 1
            
            answer_dict[dir_id] = {"name": dir_name, "full_name": dir_full_name, "level": dir['level']}
        
        return answer_dict
            

    def get_cskb_incremental_knowledge(self, agent_id: str, knowledge_type: str, query_date: str = None) -> list:
        """"根据agent_id和时间获取新增和更新的CSKB知识"""
        # 确定表名和字段列表
        if knowledge_type == 'doc':
            table = 'cskb_doc_pub'
            fields = get_doc_sql_fields()  # 获取需要的字段列表
        elif knowledge_type == 'faq':
            table = 'cskb_faq_pub'
            fields = get_faq_sql_fields()  # 获取需要的字段列表
        else:
            raise ValueError(f"不支持的知识类型: {knowledge_type}")

        # 处理查询日期
        if not query_date:
            yesterday = datetime.date.today() - datetime.timedelta(days=1)
            query_date = yesterday.strftime('%Y-%m-%d')
        else:
            query_date = datetime.datetime.strptime(query_date, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')

        # 使用参数化查询，只查询需要的字段
        sql = f'SELECT {fields} FROM {table} WHERE agent_id = %s AND version_status = 1 AND DATE(sys_updated) = %s and attaches <> \'null\' and attaches <> \'[]\' LIMIT 1'
        results = self.db.execute_query(sql, (agent_id, query_date))

        # 映射查询结果
        mapped_results = []
        if results:
            if knowledge_type == 'doc':
                mapped_result = map_doc_result(results)
            elif knowledge_type == 'faq':
                mapped_result = map_faq_result(results)
            else:
                mapped_result = []

            # 确保返回列表格式
            if isinstance(mapped_result, list):
                mapped_results = mapped_result
            elif mapped_result:
                mapped_results = [mapped_result]

        return mapped_results

    def get_cskb_deleted_knowledge(self, agent_id: str, knowledge_type: str, query_date: str = None) -> list:
        """"根据agent_id和时间获取删除的CSKB知识"""
        # 确定表名和字段列表
        if knowledge_type == 'doc':
            table = 'cskb_doc_pub'
            fields = get_doc_sql_fields()  # 获取需要的字段列表
        elif knowledge_type == 'faq':
            table = 'cskb_faq_pub'
            fields = get_faq_sql_fields()  # 获取需要的字段列表
        else:
            raise ValueError(f"不支持的知识类型: {knowledge_type}")

        # 处理查询日期
        if not query_date:
            yesterday = datetime.date.today() - datetime.timedelta(days=1)
            query_date = yesterday.strftime('%Y-%m-%d')
        else:
            query_date = datetime.datetime.strptime(query_date, '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')

        # 使用参数化查询，只查询需要的字段
        sql = f'SELECT {fields} FROM {table} WHERE agent_id = %s AND delete_flag = 1 AND DATE(sys_updated) = %s LIMIT 1'
        results = self.db.execute_query(sql, (agent_id, query_date))

        # 映射查询结果
        mapped_results = []
        if results:
            if knowledge_type == 'doc':
                mapped_result = map_doc_result(results)
            elif knowledge_type == 'faq':
                mapped_result = map_faq_result(results)
            else:
                mapped_result = []

            # 确保返回列表格式
            if isinstance(mapped_result, list):
                mapped_results = mapped_result
            elif mapped_result:
                mapped_results = [mapped_result]

        return mapped_results



    def _process_knowledge_list(self, knowledge_list: list, knowledge_type: str, agent: dict, dir_tree_dict: dict) -> list:
        """通用的知识列表处理方法"""
        if not knowledge_list:
            return []

        processed_list = []

        # 初始化带进度条的迭代器
        progress = tqdm(
            knowledge_list,
            desc=f"处理 {agent['agent_name']} 增量{knowledge_type.upper()}",
            bar_format="{l_bar}{bar:20}{r_bar}",
            ncols=100,  # 控制进度条总宽度
            mininterval=0.5  # 更新频率
        )

        for item in progress:
            if knowledge_type == 'doc':
                # 使用通用方法处理文档和附件
                doc_knowledge_list = self._process_document_with_attachments(item, agent, dir_tree_dict)
                processed_list.extend(doc_knowledge_list)
            elif knowledge_type == 'faq':
                # 处理FAQ
                if item["editType"] == 1:
                    action = "add"
                elif item["editType"] == 2:
                    action = "update"
                
                self._enrich_faq_with_directory_info(item, dir_tree_dict, agent['agent_name'])
                processed_list.append({"type": "faq", "data": item, "action": action})

        return processed_list

    def _enrich_faq_with_directory_info(self, faq, dir_tree_dict, agent_name):
        """为FAQ添加目录信息"""
        faq['agentName'] = agent_name
        faq_dir_id = faq['dirId']
        faq['dir_name'] = f"[{agent_name}] {dir_tree_dict[faq_dir_id]['full_name']}"
        faq['dir_level'] = dir_tree_dict[faq_dir_id]['level']

    def get_all_updated_cskb_results(self, agent: dict, query_date: str = None):
        """合并文档和问答的增量结果"""
        agent_id = agent['agent_id']
        knowledge_list = []
        dir_tree_dict = self.get_directory_tree(agent)

        # 获取增量和删除的知识数据
        incremental_docs = self.get_cskb_incremental_knowledge(agent_id=agent_id, knowledge_type='doc', query_date=query_date)
        incremental_faqs = self.get_cskb_incremental_knowledge(agent_id=agent_id, knowledge_type='faq', query_date=query_date)
        deleted_docs = self.get_cskb_deleted_knowledge(agent_id=agent_id, knowledge_type='doc', query_date=query_date)
        deleted_faqs = self.get_cskb_deleted_knowledge(agent_id=agent_id, knowledge_type='faq', query_date=query_date)

        # 处理增量文档
        doc_results = self._process_knowledge_list(incremental_docs, 'doc', agent, dir_tree_dict)
        knowledge_list.extend(doc_results)

        # 处理增量FAQ
        faq_results = self._process_knowledge_list(incremental_faqs, 'faq', agent, dir_tree_dict)
        knowledge_list.extend(faq_results)

        # 处理删除的数据（标记为删除状态）
        deleted_results = self._process_deleted_knowledge(deleted_docs, deleted_faqs, agent, dir_tree_dict)
        knowledge_list.extend(deleted_results)

        return knowledge_list


    def _process_deleted_knowledge(self, deleted_docs: list, deleted_faqs: list, agent: dict, dir_tree_dict: dict) -> list:
        """处理删除的知识数据"""
        deleted_list = []

        # 处理删除的文档
        for doc in deleted_docs:
            self._enrich_document_with_directory_info(doc, dir_tree_dict, agent['agent_name'])
            deleted_list.append({"type": "doc", "data": doc, "action": "delete"})

        # 处理删除的FAQ
        for faq in deleted_faqs:
            self._enrich_faq_with_directory_info(faq, dir_tree_dict, agent['agent_name'])
            deleted_list.append({"type": "faq", "data": faq, "action": "delete"})

        return deleted_list

    def upload_update_cskb_knowledge(self, agent: dict, knowledge_list: list):
        """上传或更新CSKB的知识到Milvus"""
        database='CSKB_' + agent['agent_name_en']
        for action in ['add', 'update', 'delete']:
            action_knowledge_list = [item for item in knowledge_list if item['action'] == action]
            if action == 'add':
                chunk_list = load_and_chunk_base(agent, action_knowledge_list)
                upload_chunk(agent, chunk_list)
            elif action == 'delete':
                for cskb_type in ['doc', 'faq', 'attach']:
                    if cskb_type == 'doc':
                        delete_ids = [item['data']['id'] for item in action_knowledge_list if item['type'] == 'doc']
                    elif cskb_type == 'faq':
                        delete_ids = [item['data']['id'] for item in action_knowledge_list if item['type'] == 'faq']                       
                    else:
                        # 如果为attach，删除后原表
                        delete_ids = [item['data']['id'] for item in action_knowledge_list if item['type'] == 'attach']
                    if delete_ids:
                        quoted_ids = [f"'{doc_id}'" for doc_id in delete_ids]
                        filter_expr = f"metadata['doc_id'] in [{','.join(quoted_ids)}]"
                        delete_by_condition(database=database, collection=cskb_type, filter_expr=filter_expr)
            else:
                for cskb_type in ['doc', 'faq', 'attach']:
                    if cskb_type == 'doc':
                        delete_ids = [item['data']['originalId'] for item in action_knowledge_list if item['type'] == 'doc']
                        delete_id = 'source_id'
                    elif cskb_type == 'faq':
                        delete_ids = [item['data']['originalId'] for item in action_knowledge_list if item['type'] == 'faq']
                        delete_id = 'source_id'               
                    else:
                        delete_ids = [item['data']['originalId_parent'] for item in action_knowledge_list if item['type'] == 'attach']
                        delete_id = 'source_id_parent'
                    if delete_ids:
                        quoted_ids = [f"'{id}'" for id in delete_ids]
                        filter_expr = f"metadata['{delete_id}'] in [{','.join(quoted_ids)}]"
                        delete_by_condition(database=database, collection=cskb_type, filter_expr=filter_expr)
                chunk_list = load_and_chunk_base(agent, action_knowledge_list)
                upload_chunk(agent, chunk_list)
            



if __name__ == "__main__":
    db = 'cskb'
    cskb_operation = CSKBOperation(db=db)
    agent = {'id': 5, 'agent_id': '007c529739690a861ad158e4237fea06', 'agent_name': '上汽大众客服知识库', 'access_token': '224ee926-559d-4435-b95a-098076e67353', 'env': 'prod'}
    query_date = '2023-07-05 19:54:24'
    agent_id = '007c529739690a861ad158e4237fea06'
    # results = cskb_operation.get_cskb_incremental_knowledge(agent_id=agent_id, knowledge_type='doc', query_date=query_date)
    # print(results)

    # 测试增量更新方法
    results = cskb_operation.get_updated_cskb_results(agent=agent, query_date=query_date)
    print(f"获取到 {len(results)} 条增量知识")
    print(results)

    # 测试获取所有知识
    # results = cskb_operation.get_agent_all_knowledge(agent)
    # print(results)

    