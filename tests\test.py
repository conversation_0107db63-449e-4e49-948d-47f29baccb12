from langchain_text_splitters import RecursiveCharacterTextSplitter

# Load example document
with open("README_data_mapper.md", encoding="utf-8") as f:
    state_of_the_union = f.read()

text_splitter = RecursiveCharacterTextSplitter(
    # Set a really small chunk size, just to show.
    chunk_size=100,
    chunk_overlap=20,
    length_function=len,
    is_separator_regex=False,
)
texts = text_splitter.create_documents([state_of_the_union])
print(texts)