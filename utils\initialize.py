from KBQuery.core.embedding import merge_vector_index
from config import KN<PERSON>LEDGE_FIELDS
from KBQuery.KBQuery import load_knowledge_base

# 向量合并，需要时调用
# merge_vector_index(origin_index_name="RISE", new_index_name="RISE_1_CSGA_1", type="doc")
# merge_vector_index(origin_index_name="RISE_base", new_index_name="RISE_1", type="faq")


# 初始化知识向量（通用函数）
def init_knowledge_vectors(knowledge_fields: list[str]):
    knowledge_vectors = {}

    for knowledge_field in knowledge_fields:
        doc_index = load_knowledge_base(knowledge_field=knowledge_field, type="doc")        # 初始化文档知识向量索引

        # 初始化FAQ知识向量索引
        # 以下几个知识领域配置没有FAQ知识库，不需要初始化FAQ向量库
        # if knowledge_field not in ['EDI-WEB', 'RISE', 'WISECCP', 'D-FMEA']:
        faq_index = load_knowledge_base(knowledge_field=knowledge_field, type="faq")        
        # else:
            # faq_index = None

        knowledge_vectors[knowledge_field] = {"doc_index": doc_index, "faq_index": faq_index}
        
    return knowledge_vectors

# 初始化知识向量（函数调用）
KNOWLEDGE_VECTORS = init_knowledge_vectors(KNOWLEDGE_FIELDS)