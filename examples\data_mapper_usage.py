"""
数据映射器使用示例
演示如何使用utils.data_mapper模块处理MySQL查询结果的字段映射
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.data_mapper import (
    data_mapper, 
    map_doc_result, 
    map_faq_result,
    get_doc_sql_fields,
    get_faq_sql_fields
)
from database.mysql import MySQLDatabase


def example_field_mapping_concept():
    """
    示例1: 字段映射概念演示
    """
    print("=== 示例1: 字段映射概念 ===")
    
    # 模拟MySQL查询结果（使用实际的MySQL字段名）
    mysql_doc_result = {
        'id': 'doc_001',
        'original_id': 'orig_001',           # MySQL字段名
        'title': '测试文档标题',
        'agent_id': 'agent_001',             # MySQL字段名
        'agent_name': '测试智能体',           # MySQL字段名
        'dir_id': 'dir_001',                 # MySQL字段名
        'pub_user_name': '张三',             # MySQL字段名
        'pub_time': '2024-01-01 10:00:00',   # MySQL字段名
        'word_num': 1500,                    # MySQL字段名
        'picture_num': 3,                    # MySQL字段名
        'link_num': 2,                       # MySQL字段名
        'doc_text': '文档正文内容...',        # MySQL字段名
        'doc_html': '<p>HTML格式内容</p>',    # MySQL字段名
    }
    
    print("原始MySQL查询结果:")
    for key, value in mysql_doc_result.items():
        print(f"  {key}: {value}")
    
    # 使用数据映射器转换
    mapped_result = map_doc_result(mysql_doc_result, use_dict_cursor=True)
    
    print("\n映射后的标准格式:")
    for key, value in mapped_result.items():
        print(f"  {key}: {value}")
    
    print("\n字段名对比:")
    print("  MySQL字段名 -> 目标字段名")
    print("  original_id -> originalId")
    print("  agent_id -> agentId")
    print("  pub_user_name -> pubUserName")
    print("  word_num -> wordNum")
    print("  等等...")


def example_sql_generation():
    """
    示例2: SQL查询语句生成
    """
    print("=== 示例2: SQL查询语句生成 ===")
    
    # 获取MySQL字段名列表（用于查询）
    mysql_doc_fields = get_doc_sql_fields(use_mapping=True)
    print("MySQL文档字段列表:")
    print(f"  {mysql_doc_fields}")
    
    # 生成完整的SQL查询语句
    doc_sql = f"""
    SELECT {mysql_doc_fields}
    FROM cskb_doc_pub 
    WHERE agent_id = %s 
    AND version_status = 1
    LIMIT 10
    """
    print(f"\n生成的文档查询SQL:")
    print(doc_sql)
    
    # FAQ查询示例
    mysql_faq_fields = get_faq_sql_fields(use_mapping=True)
    faq_sql = f"""
    SELECT {mysql_faq_fields}
    FROM cskb_faq_pub 
    WHERE agent_id = %s
    LIMIT 5
    """
    print(f"\n生成的FAQ查询SQL:")
    print(faq_sql)


def example_real_database_integration():
    """
    示例3: 实际数据库集成示例
    """
    print("=== 示例3: 实际数据库集成 ===")
    
    try:
        # 创建数据库连接
        db = MySQLDatabase("cskb")
        
        # 使用映射器生成SQL字段列表
        mysql_fields = get_doc_sql_fields(use_mapping=True)
        
        # 构建查询SQL（注意：这里需要根据实际表结构调整字段名）
        sql = f"""
        SELECT 
            id,
            original_id,
            '' as parent_id,
            title,
            agent_id,
            '' as agent_name,
            dir_id,
            '' as original_parent_id,
            pub_user_name,
            pub_time,
            version,
            tags,
            attaches,
            word_num,
            picture_num,
            link_num,
            '' as doc_text,
            '' as doc_html,
            '' as directory_name,
            0 as directory_level
        FROM cskb_doc_pub 
        LIMIT 3
        """
        
        # 执行查询
        results = db.execute_query(sql)
        print(f"查询到 {len(results)} 条记录")
        
        if results:
            # 使用数据映射器转换结果
            mapped_results = map_doc_result(results, use_dict_cursor=True)
            
            print("映射后的第一条记录:")
            for key, value in mapped_results[0].items():
                print(f"  {key}: {value}")
        
        db.close()
        
    except Exception as e:
        print(f"数据库查询示例执行失败: {e}")


def example_batch_processing():
    """
    示例4: 批量处理示例
    """
    print("=== 示例4: 批量处理示例 ===")
    
    # 模拟多条MySQL查询结果
    mysql_faq_results = [
        {
            'id': 'faq_001',
            'agent_id': 'agent_001',
            'agent_name': '测试智能体',
            'question': '如何使用系统？',
            'answer': '请参考用户手册...',
            'dir_id': 'dir_001',
            'original_id': 'orig_faq_001',
            'pub_user_name': '李四',
            'pub_time': '2024-01-01 11:00:00',
            'version_status': 1,
        },
        {
            'id': 'faq_002',
            'agent_id': 'agent_001',
            'agent_name': '测试智能体',
            'question': '系统支持哪些功能？',
            'answer': '系统支持文档管理...',
            'dir_id': 'dir_002',
            'original_id': 'orig_faq_002',
            'pub_user_name': '王五',
            'pub_time': '2024-01-01 12:00:00',
            'version_status': 1,
        }
    ]
    
    print("原始MySQL查询结果（2条FAQ记录）:")
    for i, faq in enumerate(mysql_faq_results):
        print(f"  FAQ {i+1}: {faq['question']}")
    
    # 批量映射
    mapped_faqs = map_faq_result(mysql_faq_results, use_dict_cursor=True)
    
    print("\n映射后的结果:")
    for i, faq in enumerate(mapped_faqs):
        print(f"  FAQ {i+1}:")
        print(f"    id: {faq['id']}")
        print(f"    agentId: {faq['agentId']}")  # 注意字段名已转换
        print(f"    question: {faq['question']}")
        print(f"    versionStatus: {faq['versionStatus']}")  # version_status -> versionStatus


def example_custom_mapping():
    """
    示例5: 自定义映射配置
    """
    print("=== 示例5: 自定义映射配置 ===")
    
    # 如果需要自定义映射，可以创建新的DataMapper实例
    from utils.data_mapper import DataMapper
    
    custom_mapper = DataMapper()
    
    # 可以修改映射配置
    custom_doc_mapping = custom_mapper.doc_mapping.copy()
    custom_doc_mapping['custom_field'] = 'customField'  # 添加自定义字段映射
    
    print("自定义映射配置示例:")
    print("  可以通过修改DataMapper实例的映射配置来适应不同的数据库表结构")
    print("  例如：添加新字段映射、修改现有映射关系等")


def practical_integration_example():
    """
    示例6: 实际项目集成建议
    """
    print("=== 示例6: 实际项目集成建议 ===")
    
    print("""
    在实际项目中的使用建议:
    
    1. 在数据访问层使用:
       - 在DAO或Repository类中使用数据映射器
       - 统一处理所有数据库查询结果的字段映射
    
    2. 配置管理:
       - 将字段映射配置放在utils/data_mapper.py中
       - 根据实际数据库表结构调整映射配置
    
    3. 错误处理:
       - 在映射过程中添加适当的日志记录
       - 处理字段缺失或类型不匹配的情况
    
    4. 性能优化:
       - 对于大量数据的批量处理，考虑使用生成器
       - 缓存映射配置，避免重复计算
    
    5. 代码组织建议:
       - utils/data_mapper.py: 核心映射逻辑
       - database/: 数据库访问层，使用映射器
       - models/: 数据模型定义
       - services/: 业务逻辑层，使用映射后的标准数据
    """)


if __name__ == "__main__":
    example_field_mapping_concept()
    print("\n" + "="*60 + "\n")
    
    example_sql_generation()
    print("\n" + "="*60 + "\n")
    
    example_real_database_integration()
    print("\n" + "="*60 + "\n")
    
    example_batch_processing()
    print("\n" + "="*60 + "\n")
    
    example_custom_mapping()
    print("\n" + "="*60 + "\n")
    
    practical_integration_example()
